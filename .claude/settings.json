{"permissions": {"defaultMode": "bypassPermissions", "disableBypassPermissionsMode": false, "allow": ["*", "Bash(*)", "Read(*)", "Write(*)", "Edit(*)", "MultiEdit(*)", "Glob(*)", "Grep(*)", "LS(*)", "WebFetch(*)", "WebSearch(*)", "Task(*)", "TodoRead(*)", "TodoWrite(*)", "NotebookRead(*)", "NotebookEdit(*)", "mcp__ide__getDiagnostics(*)", "mcp__ide__executeCode(*)", "exit_plan_mode(*)"], "deny": [], "additionalDirectories": ["../", "../../", "../../../", "/Users/<USER>/", "/Users/<USER>/Developer/", "/tmp/", "/var/"], "autoApprove": true, "alwaysAllow": true}, "defaultMode": "bypassPermissions"}