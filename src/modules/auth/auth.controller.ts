import { Controller, Post, Body, Get, UseGuards } from "@nestjs/common";
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
} from "@nestjs/swagger";
import {
  LoginResponseDto,
  RefreshTokenResponseDto,
  MessageResponseDto,
  UserResponseDto,
} from "../../common/dto/responses.dto";

import { AuthService } from "./auth.service";
import { JwtAuthGuard } from "../../common/guards/jwt-auth.guard";
import { CurrentUser } from "../../common/decorators/current-user.decorator";
import { LoginDto } from "./dto/login.dto";
import { RefreshTokenDto } from "./dto/refresh-token.dto";
import { ChangePasswordDto } from "./dto/change-password.dto";

@ApiTags("认证授权")
@Controller("auth")
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({ summary: "用户登录" })
  @ApiResponse({ status: 200, description: "登录成功", type: LoginResponseDto })
  @ApiResponse({ status: 401, description: "用户名或密码错误" })
  @Post("login")
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto);
  }

  @ApiOperation({ summary: "刷新令牌" })
  @ApiResponse({
    status: 200,
    description: "刷新成功",
    type: RefreshTokenResponseDto,
  })
  @ApiResponse({ status: 401, description: "刷新令牌无效" })
  @Post("refresh")
  async refresh(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto.refresh_token);
  }

  @ApiOperation({ summary: "用户登出" })
  @ApiResponse({
    status: 200,
    description: "登出成功",
    type: MessageResponseDto,
  })
  @ApiResponse({ status: 401, description: "未授权" })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post("logout")
  async logout() {
    return { message: "登出成功" };
  }

  @ApiOperation({ summary: "获取当前用户信息" })
  @ApiResponse({ status: 200, description: "获取成功", type: UserResponseDto })
  @ApiResponse({ status: 401, description: "未授权" })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Get("profile")
  async getProfile(@CurrentUser() user: any) {
    return this.authService.getProfile(user.id);
  }

  @ApiOperation({ summary: "修改密码" })
  @ApiResponse({
    status: 200,
    description: "修改成功",
    type: MessageResponseDto,
  })
  @ApiResponse({ status: 400, description: "原密码错误" })
  @ApiResponse({ status: 401, description: "未授权" })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Post("password")
  async changePassword(
    @CurrentUser() user: any,
    @Body() changePasswordDto: ChangePasswordDto
  ) {
    return this.authService.changePassword(user.id, changePasswordDto);
  }
}
